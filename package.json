{"name": "apex", "version": "8.0.0", "license": "", "scripts": {"imageoptim": "imageoptim --imagealpha 'src/assets/**/*.png'", "build:prod": "ng build --prod --aot --build-optimizer && gzipper compress --verbose ./dist", "gzipper": "gzipper", "compress": "gzipper compress ./dist", "ng": "ng", "start": "ng serve", "prebuild": "node generate-version.js", "build": "ng build --prod --aot", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@agm/core": "1.0.0-beta.5", "@angular/animations": "8.0.0", "@angular/common": "8.0.0", "@angular/compiler": "8.0.0", "@angular/core": "8.0.0", "@angular/forms": "8.0.0", "@angular/platform-browser": "8.0.0", "@angular/platform-browser-dynamic": "8.0.0", "@angular/router": "8.0.0", "@ng-bootstrap/ng-bootstrap": "4.1.3", "@ng-select/ng-select": "2.20.0", "@ngrx/store": "7.4.0", "@ngx-translate/core": "11.0.1", "@ngx-translate/http-loader": "4.0.0", "@swimlane/ngx-charts": "11.1.0", "@types/chartist": "0.9.46", "@types/d3-shape": "1.3.1", "angular-calendar": "0.27.8", "bootstrap": "4.3.1", "chartist": "0.11.2", "classlist.js": "1.1.20150312", "core-js": "3.1.3", "date-fns": "1.30.1", "gulp": "4.0.2", "hopscotch": "0.3.1", "html2canvas": "^1.3.3", "jspdf": "^2.4.0", "md5-typescript": "^1.0.5", "ng-chartist": "3.1.0", "ng2-completer": "2.0.8", "ng2-dragula": "2.1.1", "ng2-file-upload": "^1.3.0", "ng2-img-cropper": "0.9.0", "ng2-nouislider": "1.8.2", "ng2-smart-table": "1.4.0", "ngx-chips": "2.0.0-beta.0", "ngx-mask": "^8.0.0", "ngx-perfect-scrollbar": "7.2.1", "ngx-quill": "5.1.0", "ngx-toastr": "10.0.4", "ngx-ui-switch": "8.0.1", "nouislider": "13.1.5", "prismjs": "1.16.0", "quill": "1.3.6", "rxjs": "6.5.2", "screenfull": "4.2.0", "sweetalert2": "8.11.6", "web-animations-js": "github:angular/web-animations-js#release_pr208", "zone.js": "~0.9.1"}, "devDependencies": {"@angular-devkit/build-angular": "~0.800.0", "@angular/cli": "^12.2.18", "@angular/compiler-cli": "8.0.0", "@types/core-js": "2.5.0", "jasmine-core": "3.4.0", "jasmine-spec-reporter": "4.2.1", "karma": "4.1.0", "karma-chrome-launcher": "2.2.0", "karma-coverage-istanbul-reporter": "2.0.5", "karma-jasmine": "2.0.1", "karma-jasmine-html-reporter": "1.4.2", "protractor": "5.4.2", "ts-node": "8.2.0", "tslint": "5.16.0", "typescript": "3.4.5"}}