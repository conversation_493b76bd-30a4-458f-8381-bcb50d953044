{"compileOnSave": false, "compilerOptions": {"importHelpers": true, "module": "esnext", "outDir": "./dist/out-tsc", "baseUrl": "src", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es5", "typeRoots": ["node_modules/@types"], "lib": ["es2016", "dom"]}, "angularCompilerOptions": {"preserveWhitespaces": false}}