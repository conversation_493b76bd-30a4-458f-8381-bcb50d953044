{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"matngular": {"root": "", "sourceRoot": "src", "projectType": "application", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "polyfills": "src/polyfills.ts", "assets": ["src/assets", "src/favicon.ico"], "styles": ["node_modules/sweetalert2/dist/sweetalert2.min.css", "src/assets/css/wizard.css", "src/assets/css/quill.snow.css", "src/assets/css/quill.bubble.css", "src/assets/css/hopscotch.css", "node_modules/angular-calendar/scss/angular-calendar.scss", "node_modules/dragula/dist/dragula.css", "node_modules/ngx-toastr/toastr.css", "node_modules/videogular2/fonts/videogular.css", "node_modules/@swimlane/ngx-datatable/release/index.css", "node_modules/@swimlane/ngx-datatable/release/assets/icons.css", "src/assets/css/demo.css", "src/assets/sass/app.scss", "src/styles.css"], "scripts": ["src/assets/vendor/pace/pace.min.js"]}, "configurations": {"production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": true, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}]}, "test": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test.ts"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "matngular:build"}, "configurations": {"production": {"browserTarget": "matngular:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "matngular:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "karmaConfig": "./karma.conf.js", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "scripts": ["src/assets/vendor/pace/pace.min.js"], "styles": ["node_modules/sweetalert2/dist/sweetalert2.min.css", "src/assets/css/wizard.css", "src/assets/css/quill.snow.css", "src/assets/css/quill.bubble.css", "src/assets/css/hopscotch.css", "node_modules/angular-calendar/scss/angular-calendar.scss", "node_modules/dragula/dist/dragula.css", "node_modules/ngx-toastr/toastr.css", "node_modules/videogular2/fonts/videogular.css", "node_modules/@swimlane/ngx-datatable/release/index.css", "node_modules/@swimlane/ngx-datatable/release/assets/icons.css", "src/assets/css/demo.css", "src/assets/sass/app.scss", "src/styles.css"], "assets": ["src/assets", "src/favicon.ico"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": ["**/node_modules/**/*"]}}}}, "matngular-e2e": {"root": "", "sourceRoot": "", "projectType": "application", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "./protractor.conf.js", "devServerTarget": "matngular:serve"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["e2e/tsconfig.e2e.json"], "exclude": ["**/node_modules/**/*"]}}}}}, "defaultProject": "matngular", "schematics": {"@schematics/angular:component": {"prefix": "app", "styleext": "scss"}, "@schematics/angular:directive": {"prefix": "app"}}}