#### Version 8.0

##### 16-06-2019 [8.0]

### **Angular**

#### New

1.  Updated Angular to 8.0.0
2.  Updated various other packages to latest versions
3.  Updated Documentation
4.  Breaking changes in sweetalert2 with latest version

#### Fixed

1. Content layout issue 
2. Minor bug in Chat and taskboard page
3. Styling issues in FAQ and Knowledge base page

#### Version 7.0

##### 30-03-2019 [7.0]

### **Angular**

#### Added

1.  Dark Layout
2.  Transparent Layout with new colors and glass images
3.  Layout Configuration File
4.  Table Component

#### Updated

1.  Documentation
2.  New Layout For Authentication Pages (Login, Registration, Forgot Password, Lock Screen, Coming Soon, Maintenance.)
3.  Email App
4.  Taskboard App
5.  Updated All Dependencies
6.  Improved Customizer

#### Removed

1.  Removed all jquery code
2.  Removed jquery wizard component

#### Fixed

1.  UI Kit pages (Grid, Helper Classes, Typography, Text Utilities etc..)
2.  Classes according to latest bootstrap i.e changed `.card-body` to `.card-body` and added `.card-content` as a wrapper to `.card-body`

### **HTML**

#### Added

1.  Dark Layout
2.  Transparent Layout with new colors and glass images
3.  Bootstrap Spinner Component
4.  Bootstrap Toast Component
5.  Bootstrap Radio, Checkbox and IOS style switch

#### Updated

1.  Bootstrap to latest version (4.3)
2.  New Layout For Authentication Pages (Login, Registration, Forgot Password, Lock Screen, Coming Soon, Maintenance.)
3.  Updated All Dependencies
4.  Updated Documentation
5.  Improved Customizer

#### Fixed

1.  Classes according to latest bootstrap i.e changed `.card-body` to `.card-body` and added `.card-content` as a wrapper to `.card-body`
2.  UI Kit pages (Grid, Helper Classes, Typography, Text Utilities etc..)
3.  Scrollbar on Chat Page
4.  Fullscreen Mode

#### Version 6.0

##### 11-07-2018 [6.0]

#### Added

1.  Angular 7 in Apex Angular template and Starter kit
2.  ArchWizard component in forms section
3.  Select component in extra component section

#### Updated

1.  All packages updated to latest version.
2.  drag and drop component and switch component have major version upgrade and code changes
3.  Documentation

#### Fixed

1.  Minor design fixes

#### Version 5.1

##### 07-24-2018 [5.1]

#### Added

1.  RTL version (Demo available)

#### Updated

1.  Packages updated to latest version.
2.  Documentation

#### Fixed

1.  Navbar issue in small devices

#### Version 5.0

##### 06-09-2018 [5.0]

#### Added

1.  Angular 6 Latest version

#### Updated

1.  Angular, NgRx, NG-bootstrap and other Packages updated to latest version.

#### Fixed

1.  NoUI Slider while running ng-serve
2.  HTML template - navbar dropdown z-index issue

#### Version 4.1

##### 02-22-2018 [4.1]

#### Added

1.  HTML Front-end Landing Page
2.  Top fix navbar option (Demo Available)/li>
3.  HTML template - All 5 Demos now available in downloadable

#### Updated

1.  Angular, NgRx, NG-bootstrap and other Packages updated to latest version.
2.  Horizontal timeline page now fully converted to typescript version.
3.  Chat and Inbox page responsive design
4.  Documentation

#### Fixed

1.  User Profile page responsive issue.
2.  IE 11 issue.

#### Version 4.0

##### 01-26-2018 [4.0]

#### Added

1.  Chat NgRx page in Angular template
2.  Taskboard NgRx page in Angular template
3.  Tags input page in angular and html template
4.  Switch page in angular and html template

#### Updated

1.  Angular Template and Starter kit Template Angular version updated to latest versiom (5.2.1)
2.  Bootstrap updated to latest version (Bootstrap 4 Stable) in Angular Template and Starter kit Template and HTML template
3.  Sweet alert package updated to latest version.
4.  NG-Bootstrap package updated to latest version.
5.  NG-Bootstrap Components
6.  Documentation

#### Fixed

1.  Taskboard page minor bug in Angular template

#### Version 3.1

##### 12-26-2017 [3.1]

#### Added

1.  Recent Activity, Chat and Settings Sidebar tabbed panel For Angular and HTML Templates
2.  Image Cropper page For Angular and HTML Templates

#### Updated

1.  Packages for Angular Template and Starter kit Template updated to latest version
2.  Documentation

#### Fixed

1.  IOS scrolling issue
2.  Menu issue on small screen in starter kit.

#### Version 3.0

##### 12-03-2017 [3.0]

#### Added

1.  HTML(non-angular) Version

#### Version 2.1

##### 11-21-2017 [2.1]

#### Added

1.  Search Page
2.  FAQ Page
3.  Knowledge Base Page
4.  Internationalization (i18n) Support
5.  Authentication Service

#### Updated

1.  Angular Version (5.0.2)
2.  starter kit
3.  Documentation
4.  Calendar
5.  Inbox
6.  Chat
7.  NGX Datatable
8.  NGX Charts

#### Fixed

1.  Minor Bugs

#### Version 2.0

##### 11-10-2017 [2.0]

#### Added

1.  Taskboard
2.  Audio Player
3.  Video Player
4.  Chat - Audio & Video
5.  Drag n Drop
6.  Tour

#### Updated

1.  Updated to Angular 5+
2.  Updated starter kit to Angular 5+
3.  Documentation
4.  Calendar
5.  Sweet Alert
6.  Data Tables
7.  Quill Editor

#### Fixed

1.  Minor Bugs & design flaws

#### Initial release

##### 10-23-2017 [1.0]

- Initial release
